<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\GameController;

// 认证路由
Route::get('/', [AuthController::class, 'loginPage'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout']);
Route::post('/register', [AuthController::class, 'register']);

// 游戏路由 (需要登录)
Route::middleware('auth')->group(function () {
    Route::get('/game', function () {
        $currentGame = \App\Models\Game::where('status', 'active')->first();
        if (!$currentGame) {
            $currentGame = \App\Models\Game::create([
                'version_number' => 1,
                'round_number' => 1,
                'status' => 'active',
                'remaining_time' => 120,
                'result_number' => null
            ]);
        }
        return view('game.index_new', compact('currentGame'));
    })->name('game');
    Route::get('/mobile', [GameController::class, 'mobile'])->name('game.mobile');
    Route::get('/game-old', [GameController::class, 'index'])->name('game.old');
    Route::get('/api/game/status', [GameController::class, 'getStatus']);
    Route::post('/api/game/bet', [GameController::class, 'placeBet']);
});

// 后台管理路由 (可配置路径)
Route::prefix(env('ADMIN_PATH', 'admin'))->middleware('auth')->group(function () {
    Route::get('/', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('admin.dashboard');
    Route::get('/users', [App\Http\Controllers\Admin\DashboardController::class, 'users'])->name('admin.users');
    Route::get('/users/{user}', [App\Http\Controllers\Admin\DashboardController::class, 'userDetail'])->name('admin.user.detail');
    Route::post('/users/{user}/balance', [App\Http\Controllers\Admin\DashboardController::class, 'adjustBalance'])->name('admin.user.balance');
    Route::post('/users/{user}/status', [App\Http\Controllers\Admin\DashboardController::class, 'changeUserStatus'])->name('admin.user.status');
    Route::get('/bets', [App\Http\Controllers\Admin\DashboardController::class, 'bets'])->name('admin.bets');
    Route::get('/games', [App\Http\Controllers\Admin\DashboardController::class, 'games'])->name('admin.games');
    Route::get('/settings', [App\Http\Controllers\Admin\DashboardController::class, 'settings'])->name('admin.settings');
    Route::post('/settings', [App\Http\Controllers\Admin\DashboardController::class, 'updateSettings'])->name('admin.settings.update');
});
