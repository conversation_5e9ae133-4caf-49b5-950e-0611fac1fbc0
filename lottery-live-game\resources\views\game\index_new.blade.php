<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>彩票直播游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding: 0;
            border: 0;
            margin: 0;
            cursor: default;
            color: #888;
            background-color: #333;
            text-align: center;
            font-family: Helvetica, Verdana, Arial, sans-serif;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* 主容器 - 固定尺寸，居中显示 */
        .game-container {
            width: 1200px;
            height: 800px;
            margin: 0 auto;
            display: flex;
            background-color: #333;
            position: relative;
            top: 50%;
            transform: translateY(-50%);
        }

        /* 左侧区域 */
        .left-section {
            width: 480px;
            height: 800px;
            display: flex;
            flex-direction: column;
            background-color: #073430;
        }

        /* 视频区域 */
        .video-section {
            width: 100%;
            height: 300px;
            background: #000;
            position: relative;
            overflow: hidden;
        }

        .video-section iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* 游戏信息区域 */
        .game-info-section {
            width: 100%;
            height: 100px;
            background-color: #073430;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 20px;
            color: #fff;
            font-size: 24px;
        }

        .game-version, .game-round {
            text-align: center;
        }

        .countdown-section {
            text-align: center;
        }

        .countdown-label {
            font-size: 18px;
            color: #fff;
        }

        .countdown-time {
            font-size: 32px;
            color: #ff6b6b;
            font-weight: bold;
        }

        /* 历史号码区域 */
        .history-section {
            width: 100%;
            height: 200px;
            background-color: #073430;
            padding: 10px;
            overflow: hidden;
        }

        .history-numbers {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            justify-content: flex-start;
            align-content: flex-start;
            height: 100%;
            overflow: hidden;
        }

        .history-number {
            width: 50px;
            height: 60px;
            line-height: 60px;
            font-size: 30px;
            color: #fff;
            background-color: #a6a6a6;
            margin: 0;
            text-align: center;
        }

        /* 用户信息区域 */
        .user-section {
            width: 100%;
            height: 300px;
            background-color: #073430;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .user-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #fff;
            font-size: 24px;
        }

        .username {
            color: #fff;
            font-weight: bold;
        }

        .balance {
            color: orange;
        }

        .logout-btn {
            background: url(../img/button9.1201b.png) no-repeat;
            background-size: 100% 100%;
            color: #fff;
            font-size: 25px;
            line-height: 50px;
            width: 100px;
            height: 50px;
            border: none;
            cursor: pointer;
            margin: 20px auto;
        }

        .logout-btn:hover {
            opacity: 0.8;
        }

        /* 右侧投注区域 */
        .right-section {
            width: 720px;
            height: 800px;
            background-color: #333;
            display: flex;
            flex-direction: column;
        }

        /* 投注表格 */
        .betting-tables {
            flex: 1;
            padding: 10px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .bet-table {
            width: 100%;
        }

        .bet-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .bet-table td {
            padding: 0;
            margin: 0;
            border: 1px solid #555;
        }

        /* 投注按钮样式 */
        .bet-option {
            background: url(../img/button9.1201b.png) no-repeat;
            background-size: 100% 100%;
            color: #fff;
            font-size: 20px;
            height: 68px;
            line-height: 68px;
            position: relative;
            cursor: pointer;
            border: none;
            width: 100%;
            text-align: center;
        }

        .bet-option:hover {
            opacity: 0.8;
        }

        .bet-option .bet-label {
            font-size: 20px;
            margin-left: 5px;
        }

        .bet-option .bet-odds {
            position: absolute;
            top: 5px;
            right: 5px;
            height: 20px;
            line-height: 20px;
            font-size: 20px;
        }

        .bet-option .bet-amount {
            position: absolute;
            left: 5px;
            top: 20px;
            height: 30px;
            line-height: 30px;
            display: inline-block;
            margin: 0px;
            font-size: 18px;
        }

        /* 刷新按钮 */
        .refresh-section {
            height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
        }

        .refresh-btn {
            background: url(../img/xiazhu_but_blue.50180.png) no-repeat;
            background-size: 100% 100%;
            color: #fff;
            font-size: 25px;
            line-height: 50px;
            width: 120px;
            height: 50px;
            border: none;
            cursor: pointer;
        }

        .refresh-btn:hover {
            opacity: 0.8;
        }

        /* 响应式设计 */
        @media screen and (max-width: 1200px) {
            .game-container {
                width: 90vw;
                height: 60vh;
                transform: translateY(-50%) scale(0.8);
            }
        }

        @media screen and (max-width: 800px) {
            .game-container {
                width: 95vw;
                height: 50vh;
                transform: translateY(-50%) scale(0.6);
            }
        }

        /* 强制横屏 */
        @media screen and (orientation: portrait) and (max-width: 800px) {
            body {
                transform: rotate(90deg) scale(1.2);
                overflow: hidden;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- 左侧区域 -->
        <div class="left-section">
            <!-- 视频区域 -->
            <div class="video-section">
                <iframe src="" style="width: 100%; height: 100%; border: none; background: #000;"></iframe>
            </div>

            <!-- 游戏信息区域 -->
            <div class="game-info-section">
                <div class="game-version">
                    <div>第</div>
                    <div id="versionNumber">{{ $currentGame->version_number }}</div>
                    <div>版</div>
                </div>
                <div class="game-round">
                    <div>第</div>
                    <div id="roundNumber">{{ $currentGame->round_number }}</div>
                    <div>局</div>
                </div>
                <div class="countdown-section">
                    <div class="countdown-label">倒计时:</div>
                    <div class="countdown-time" id="countdown">{{ $currentGame->remaining_time }}</div>
                </div>
            </div>

            <!-- 历史号码区域 -->
            <div class="history-section">
                <div class="history-numbers" id="historyNumbers">
                    <!-- 历史号码将通过JavaScript动态填充 -->
                </div>
            </div>

            <!-- 用户信息区域 -->
            <div class="user-section">
                <div class="user-info">
                    <div class="username">{{ Auth::user()->username }}</div>
                    <div class="balance">
                        积分: <span id="userBalance">{{ number_format(Auth::user()->balance, 2) }}</span>
                    </div>
                </div>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>

        <!-- 右侧投注区域 -->
        <div class="right-section">
            <div class="betting-tables">
                <!-- 基础投注表格 -->
                <div class="bet-table">
                    <table>
                        <tr>
                            <td>
                                <button class="bet-option" data-type="small">
                                    <span class="bet-label">小(1-12)</span>
                                    <span class="bet-odds">x2</span>
                                    <span class="bet-amount" id="small-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option" data-type="zero">
                                    <span class="bet-label">0</span>
                                    <span class="bet-odds">x11</span>
                                    <span class="bet-amount" id="zero-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option" data-type="big">
                                    <span class="bet-label">大(13-24)</span>
                                    <span class="bet-odds">x2</span>
                                    <span class="bet-amount" id="big-amount">0</span>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <button class="bet-option" data-type="odd">
                                    <span class="bet-label">单</span>
                                    <span class="bet-odds">x2</span>
                                    <span class="bet-amount" id="odd-amount">0</span>
                                </button>
                            </td>
                            <td></td>
                            <td>
                                <button class="bet-option" data-type="even">
                                    <span class="bet-label">双</span>
                                    <span class="bet-odds">x2</span>
                                    <span class="bet-amount" id="even-amount">0</span>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <button class="bet-option" data-type="range1">
                                    <span class="bet-label">1-8</span>
                                    <span class="bet-odds">x3</span>
                                    <span class="bet-amount" id="range1-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option" data-type="range2">
                                    <span class="bet-label">9-16</span>
                                    <span class="bet-odds">x3</span>
                                    <span class="bet-amount" id="range2-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option" data-type="range3">
                                    <span class="bet-label">17-24</span>
                                    <span class="bet-odds">x3</span>
                                    <span class="bet-amount" id="range3-amount">0</span>
                                </button>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 区间投注表格 -->
                <div class="bet-table">
                    <table>
                        <tr>
                            <td>
                                <button class="bet-option" data-type="range4">
                                    <span class="bet-label">1-6</span>
                                    <span class="bet-odds">x4</span>
                                    <span class="bet-amount" id="range4-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option" data-type="range5">
                                    <span class="bet-label">7-12</span>
                                    <span class="bet-odds">x4</span>
                                    <span class="bet-amount" id="range5-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option" data-type="range6">
                                    <span class="bet-label">13-18</span>
                                    <span class="bet-odds">x4</span>
                                    <span class="bet-amount" id="range6-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option" data-type="range7">
                                    <span class="bet-label">19-24</span>
                                    <span class="bet-odds">x4</span>
                                    <span class="bet-amount" id="range7-amount">0</span>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <button class="bet-option" data-type="range8">
                                    <span class="bet-label">1-3</span>
                                    <span class="bet-odds">x8</span>
                                    <span class="bet-amount" id="range8-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option" data-type="range9">
                                    <span class="bet-label">7-9</span>
                                    <span class="bet-odds">x8</span>
                                    <span class="bet-amount" id="range9-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option" data-type="range10">
                                    <span class="bet-label">13-15</span>
                                    <span class="bet-odds">x8</span>
                                    <span class="bet-amount" id="range10-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option" data-type="range11">
                                    <span class="bet-label">19-21</span>
                                    <span class="bet-odds">x8</span>
                                    <span class="bet-amount" id="range11-amount">0</span>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <button class="bet-option" data-type="range12">
                                    <span class="bet-label">4-6</span>
                                    <span class="bet-odds">x8</span>
                                    <span class="bet-amount" id="range12-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option" data-type="range13">
                                    <span class="bet-label">10-12</span>
                                    <span class="bet-odds">x8</span>
                                    <span class="bet-amount" id="range13-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option" data-type="range14">
                                    <span class="bet-label">16-18</span>
                                    <span class="bet-odds">x8</span>
                                    <span class="bet-amount" id="range14-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option" data-type="range15">
                                    <span class="bet-label">22-24</span>
                                    <span class="bet-odds">x8</span>
                                    <span class="bet-amount" id="range15-amount">0</span>
                                </button>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 单号投注表格 -->
                <div class="bet-table">
                    <table>
                        <tr>
                            <td><button class="bet-option" data-type="number" data-number="1"><span class="bet-label">1</span><span class="bet-odds">x24</span><span class="bet-amount" id="number1-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="7"><span class="bet-label">7</span><span class="bet-odds">x24</span><span class="bet-amount" id="number7-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="13"><span class="bet-label">13</span><span class="bet-odds">x24</span><span class="bet-amount" id="number13-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="19"><span class="bet-label">19</span><span class="bet-odds">x24</span><span class="bet-amount" id="number19-amount">0</span></button></td>
                        </tr>
                        <tr>
                            <td><button class="bet-option" data-type="number" data-number="2"><span class="bet-label">2</span><span class="bet-odds">x24</span><span class="bet-amount" id="number2-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="8"><span class="bet-label">8</span><span class="bet-odds">x24</span><span class="bet-amount" id="number8-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="14"><span class="bet-label">14</span><span class="bet-odds">x24</span><span class="bet-amount" id="number14-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="20"><span class="bet-label">20</span><span class="bet-odds">x24</span><span class="bet-amount" id="number20-amount">0</span></button></td>
                        </tr>
                        <tr>
                            <td><button class="bet-option" data-type="number" data-number="3"><span class="bet-label">3</span><span class="bet-odds">x24</span><span class="bet-amount" id="number3-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="9"><span class="bet-label">9</span><span class="bet-odds">x24</span><span class="bet-amount" id="number9-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="15"><span class="bet-label">15</span><span class="bet-odds">x24</span><span class="bet-amount" id="number15-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="21"><span class="bet-label">21</span><span class="bet-odds">x24</span><span class="bet-amount" id="number21-amount">0</span></button></td>
                        </tr>
                        <tr>
                            <td><button class="bet-option" data-type="number" data-number="4"><span class="bet-label">4</span><span class="bet-odds">x24</span><span class="bet-amount" id="number4-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="10"><span class="bet-label">10</span><span class="bet-odds">x24</span><span class="bet-amount" id="number10-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="16"><span class="bet-label">16</span><span class="bet-odds">x24</span><span class="bet-amount" id="number16-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="22"><span class="bet-label">22</span><span class="bet-odds">x24</span><span class="bet-amount" id="number22-amount">0</span></button></td>
                        </tr>
                        <tr>
                            <td><button class="bet-option" data-type="number" data-number="5"><span class="bet-label">5</span><span class="bet-odds">x24</span><span class="bet-amount" id="number5-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="11"><span class="bet-label">11</span><span class="bet-odds">x24</span><span class="bet-amount" id="number11-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="17"><span class="bet-label">17</span><span class="bet-odds">x24</span><span class="bet-amount" id="number17-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="23"><span class="bet-label">23</span><span class="bet-odds">x24</span><span class="bet-amount" id="number23-amount">0</span></button></td>
                        </tr>
                        <tr>
                            <td><button class="bet-option" data-type="number" data-number="6"><span class="bet-label">6</span><span class="bet-odds">x24</span><span class="bet-amount" id="number6-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="12"><span class="bet-label">12</span><span class="bet-odds">x24</span><span class="bet-amount" id="number12-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="18"><span class="bet-label">18</span><span class="bet-odds">x24</span><span class="bet-amount" id="number18-amount">0</span></button></td>
                            <td><button class="bet-option" data-type="number" data-number="24"><span class="bet-label">24</span><span class="bet-odds">x24</span><span class="bet-amount" id="number24-amount">0</span></button></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- 刷新按钮 -->
            <div class="refresh-section">
                <button class="refresh-btn" onclick="refreshGame()">刷新</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentBets = {};
        let gameStatus = 'betting';
        let ws = null;

        // 基础JavaScript功能
        function logout() {
            if (confirm('确定要退出吗？')) {
                window.location.href = '/logout';
            }
        }

        function refreshGame() {
            location.reload();
        }

        // 倒计时功能
        function updateCountdown() {
            const countdownElement = document.getElementById('countdown');
            let timeLeft = parseInt(countdownElement.textContent);

            if (timeLeft > 0) {
                timeLeft--;
                countdownElement.textContent = timeLeft;
            } else {
                gameStatus = 'drawing';
                disableAllBets();
            }
        }

        // 投注功能
        function placeBet(type, number = null) {
            if (gameStatus !== 'betting') {
                alert('当前不能投注');
                return;
            }

            const betAmount = 10; // 固定投注金额
            const betKey = number ? `number${number}` : type;

            // 发送投注请求
            fetch('/api/bet', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    type: type,
                    number: number,
                    amount: betAmount
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateBetDisplay(betKey, data.totalAmount);
                    updateBalance(data.newBalance);
                } else {
                    alert(data.message || '投注失败');
                }
            })
            .catch(error => {
                console.error('投注错误:', error);
                alert('投注失败，请重试');
            });
        }

        // 更新投注显示
        function updateBetDisplay(betKey, amount) {
            const amountElement = document.getElementById(betKey + '-amount');
            if (amountElement) {
                amountElement.textContent = amount;
            }
        }

        // 更新余额显示
        function updateBalance(newBalance) {
            document.getElementById('userBalance').textContent = newBalance.toFixed(2);
        }

        // 禁用所有投注按钮
        function disableAllBets() {
            const betButtons = document.querySelectorAll('.bet-option');
            betButtons.forEach(button => {
                button.disabled = true;
                button.style.opacity = '0.5';
            });
        }

        // 启用所有投注按钮
        function enableAllBets() {
            const betButtons = document.querySelectorAll('.bet-option');
            betButtons.forEach(button => {
                button.disabled = false;
                button.style.opacity = '1';
            });
        }

        // 绑定投注按钮事件
        document.addEventListener('DOMContentLoaded', function() {
            const betButtons = document.querySelectorAll('.bet-option');
            betButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const type = this.getAttribute('data-type');
                    const number = this.getAttribute('data-number');
                    placeBet(type, number);
                });
            });

            // 初始化WebSocket连接
            initWebSocket();
        });

        // WebSocket连接
        function initWebSocket() {
            const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${wsProtocol}//${window.location.host}:8080`;

            ws = new WebSocket(wsUrl);

            ws.onopen = function() {
                console.log('WebSocket连接已建立');
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };

            ws.onclose = function() {
                console.log('WebSocket连接已关闭');
                // 5秒后重连
                setTimeout(initWebSocket, 5000);
            };

            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
            };
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(data) {
            switch(data.type) {
                case 'game_update':
                    updateGameInfo(data.game);
                    break;
                case 'bet_update':
                    updateBetAmounts(data.bets);
                    break;
                case 'result':
                    showGameResult(data.result);
                    break;
                case 'new_round':
                    startNewRound(data.game);
                    break;
            }
        }

        // 更新游戏信息
        function updateGameInfo(game) {
            document.getElementById('versionNumber').textContent = game.version_number;
            document.getElementById('roundNumber').textContent = game.round_number;
            document.getElementById('countdown').textContent = game.remaining_time;
            gameStatus = game.status;
        }

        // 更新投注金额显示
        function updateBetAmounts(bets) {
            for (const [key, amount] of Object.entries(bets)) {
                updateBetDisplay(key, amount);
            }
        }

        // 显示游戏结果
        function showGameResult(result) {
            const historyNumbers = document.getElementById('historyNumbers');
            const newNumber = document.createElement('div');
            newNumber.className = 'history-number';
            newNumber.textContent = result.number;

            // 添加到历史记录开头
            historyNumbers.insertBefore(newNumber, historyNumbers.firstChild);

            // 保持最多显示40个历史号码
            while (historyNumbers.children.length > 40) {
                historyNumbers.removeChild(historyNumbers.lastChild);
            }
        }

        // 开始新一轮
        function startNewRound(game) {
            gameStatus = 'betting';
            enableAllBets();

            // 清空投注金额显示
            const amountElements = document.querySelectorAll('.bet-amount');
            amountElements.forEach(element => {
                element.textContent = '0';
            });

            updateGameInfo(game);
        }

        // 每秒更新倒计时
        setInterval(updateCountdown, 1000);
    </script>
</body>
</html>
