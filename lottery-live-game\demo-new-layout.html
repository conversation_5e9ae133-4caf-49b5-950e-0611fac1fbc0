<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>彩票直播游戏 - 新布局演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding: 0;
            border: 0;
            margin: 0;
            cursor: default;
            color: #888;
            background-color: #333;
            text-align: center;
            font-family: Helvetica, Verdana, Arial, sans-serif;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* 主容器 - 占据屏幕90%，居中显示 */
        .game-container {
            width: 90vw;
            height: 90vh;
            margin: 0 auto;
            display: flex;
            background-color: #333;
            position: relative;
            top: 50%;
            transform: translateY(-50%);
            min-width: 1000px;
            min-height: 600px;
            border: 2px solid #555;
        }

        /* 左侧区域 */
        .left-section {
            width: 40%;
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #073430;
        }

        /* 视频区域 */
        .video-section {
            width: 100%;
            height: 40%;
            background: #000;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 2vw;
            border-bottom: 1px solid #555;
        }

        /* 游戏信息区域 */
        .game-info-section {
            width: 100%;
            height: 10%;
            background-color: #073430;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 5px 10px;
            color: #fff;
            font-size: 1.8vw;
            border-bottom: 1px solid #555;
        }

        .game-version, .game-round, .countdown-section {
            text-align: center;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .countdown-time {
            font-size: 2vw;
            color: #ff6b6b;
            font-weight: bold;
        }

        /* 历史号码区域 */
        .history-section {
            width: 100%;
            height: 30%;
            background-color: #073430;
            padding: 5px;
            overflow: hidden;
            border-bottom: 1px solid #555;
        }

        .history-numbers {
            display: grid;
            grid-template-columns: repeat(9, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 3px;
            height: 100%;
            width: 100%;
        }

        .history-number {
            font-size: 1.5vw;
            color: #fff;
            background-color: #a6a6a6;
            margin: 0;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            min-font-size: 14px;
        }

        /* 用户信息区域 */
        .user-section {
            width: 100%;
            height: 20%;
            background-color: #073430;
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .user-avatar {
            width: 3vw;
            height: 3vw;
            background-color: #666;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 1.5vw;
            min-width: 40px;
            min-height: 40px;
        }

        .username {
            color: #fff;
            font-weight: bold;
            font-size: 1.5vw;
        }

        .balance {
            color: orange;
            font-size: 1.5vw;
        }

        .logout-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: #fff;
            font-size: 1.3vw;
            line-height: 2.5vw;
            width: 6vw;
            height: 2.5vw;
            border: none;
            cursor: pointer;
            border-radius: 8px;
            min-width: 60px;
            min-height: 30px;
        }

        .logout-btn:hover {
            opacity: 0.8;
        }

        /* 右侧投注区域 */
        .right-section {
            width: 60%;
            height: 100%;
            background-color: #333;
            display: flex;
            flex-direction: column;
        }

        /* 投注表格 */
        .betting-tables {
            flex: 1;
            padding: 5px;
            display: flex;
            flex-direction: column;
            gap: 3px;
            overflow-y: auto;
            height: 100%;
        }

        .bet-table {
            width: 100%;
        }

        .bet-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .bet-table td {
            padding: 1px;
            margin: 0;
        }

        /* 投注按钮样式 */
        .bet-option {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: #fff;
            font-size: 1vw;
            height: 2.8vw;
            line-height: 2.8vw;
            position: relative;
            cursor: pointer;
            border: none;
            width: 100%;
            text-align: center;
            border-radius: 4px;
            min-height: 35px;
            min-font-size: 12px;
        }

        .bet-option:hover {
            opacity: 0.8;
        }

        .bet-option .bet-label {
            font-size: 1vw;
            margin-left: 3px;
            min-font-size: 12px;
        }

        .bet-option .bet-odds {
            position: absolute;
            top: 0.1vw;
            right: 0.1vw;
            height: 1vw;
            line-height: 1vw;
            font-size: 0.8vw;
            min-font-size: 10px;
        }

        .bet-option .bet-amount {
            position: absolute;
            left: 0.1vw;
            top: 1vw;
            height: 1.2vw;
            line-height: 1.2vw;
            display: inline-block;
            margin: 0px;
            font-size: 0.8vw;
            min-font-size: 10px;
        }

        /* 刷新按钮 */
        .refresh-section {
            height: 6%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 5px;
        }

        .refresh-btn {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: #fff;
            font-size: 1.5vw;
            line-height: 2.5vw;
            width: 8vw;
            height: 2.5vw;
            border: none;
            cursor: pointer;
            border-radius: 8px;
            min-width: 80px;
            min-height: 30px;
        }

        .refresh-btn:hover {
            opacity: 0.8;
        }

        /* 标题 */
        .demo-title {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            color: #fff;
            font-size: 24px;
            background: rgba(0,0,0,0.7);
            padding: 10px 20px;
            border-radius: 8px;
            z-index: 1000;
        }

        /* 移动端响应式设计 */
        @media (max-width: 768px) {
            .game-container {
                width: 100vw;
                height: 100vh;
                flex-direction: column;
                min-width: unset;
                min-height: unset;
                top: 0;
                transform: none;
                border: none;
            }

            .left-section {
                width: 100%;
                height: 50%;
                flex-direction: row;
                flex-wrap: wrap;
            }

            .video-section {
                width: 100%;
                height: 60%;
                font-size: 4vw;
            }

            .game-info-section {
                width: 100%;
                height: 15%;
                font-size: 3vw;
                padding: 2px 5px;
            }

            .countdown-time {
                font-size: 3.5vw;
            }

            .history-section {
                width: 50%;
                height: 25%;
                padding: 2px;
            }

            .history-numbers {
                grid-template-columns: repeat(6, 1fr);
                grid-template-rows: repeat(3, 1fr);
                gap: 1px;
            }

            .history-number {
                font-size: 2.5vw;
                min-font-size: 10px;
            }

            .user-section {
                width: 50%;
                height: 25%;
                padding: 2px;
                flex-direction: column;
                justify-content: center;
                gap: 2px;
            }

            .user-avatar {
                width: 6vw;
                height: 6vw;
                font-size: 3vw;
                min-width: 25px;
                min-height: 25px;
            }

            .username, .balance {
                font-size: 2.5vw;
            }

            .logout-btn {
                font-size: 2.5vw;
                line-height: 4vw;
                width: 12vw;
                height: 4vw;
                min-width: 50px;
                min-height: 25px;
            }

            .right-section {
                width: 100%;
                height: 50%;
            }

            .betting-tables {
                padding: 2px;
                gap: 2px;
            }

            .bet-option {
                font-size: 2.5vw;
                height: 6vw;
                line-height: 6vw;
                min-height: 40px;
            }

            .bet-option .bet-label {
                font-size: 2.5vw;
                min-font-size: 12px;
            }

            .bet-option .bet-odds {
                font-size: 2vw;
                height: 2vw;
                line-height: 2vw;
                min-font-size: 10px;
            }

            .bet-option .bet-amount {
                font-size: 2vw;
                height: 2.5vw;
                line-height: 2.5vw;
                top: 2.5vw;
                min-font-size: 10px;
            }

            .refresh-section {
                height: 8%;
                padding: 2px;
            }

            .refresh-btn {
                font-size: 3vw;
                line-height: 5vw;
                width: 15vw;
                height: 5vw;
                min-width: 60px;
                min-height: 30px;
            }

            .demo-title {
                font-size: 16px;
                padding: 5px 10px;
                top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-title">新布局演示 - 占据屏幕90%</div>
    
    <div class="game-container">
        <!-- 左侧区域 -->
        <div class="left-section">
            <!-- 视频区域 -->
            <div class="video-section">
                视频播放区域 (40%高度)
            </div>

            <!-- 游戏信息区域 -->
            <div class="game-info-section">
                <div class="game-version">
                    <span>第</span>
                    <span id="versionNumber">5</span>
                    <span>版</span>
                </div>
                <div class="countdown-section">
                    <span>倒计时:</span>
                    <span class="countdown-time" id="countdown">22</span>
                </div>
                <div class="game-round">
                    <span>第</span>
                    <span id="roundNumber">40</span>
                    <span>局</span>
                </div>
            </div>

            <!-- 历史号码区域 -->
            <div class="history-section">
                <div class="history-numbers" id="historyNumbers">
                    <div class="history-number">24</div>
                    <div class="history-number">1</div>
                    <div class="history-number">11</div>
                    <div class="history-number">12</div>
                    <div class="history-number">19</div>
                    <div class="history-number">6</div>
                    <div class="history-number">21</div>
                    <div class="history-number">1</div>
                    <div class="history-number">18</div>
                    <div class="history-number">11</div>
                    <div class="history-number">10</div>
                    <div class="history-number">9</div>
                    <div class="history-number">10</div>
                    <div class="history-number">24</div>
                    <div class="history-number">7</div>
                    <div class="history-number">0</div>
                    <div class="history-number">15</div>
                    <div class="history-number">3</div>
                    <div class="history-number">22</div>
                    <div class="history-number">8</div>
                    <div class="history-number">14</div>
                    <div class="history-number">2</div>
                    <div class="history-number">17</div>
                    <div class="history-number">5</div>
                    <div class="history-number">20</div>
                    <div class="history-number">13</div>
                    <div class="history-number">4</div>
                    <div class="history-number">16</div>
                    <div class="history-number">23</div>
                    <div class="history-number">9</div>
                    <div class="history-number">12</div>
                    <div class="history-number">6</div>
                    <div class="history-number">19</div>
                    <div class="history-number">1</div>
                    <div class="history-number">25</div>
                    <div class="history-number">11</div>
                </div>
            </div>

            <!-- 用户信息区域 -->
            <div class="user-section">
                <div class="user-avatar">头</div>
                <div class="username">6117</div>
                <button class="logout-btn" onclick="logout()">退出</button>
                <div class="balance">
                    积分: <span id="userBalance">1000.00</span>
                </div>
            </div>
        </div>

        <!-- 右侧投注区域 -->
        <div class="right-section">
            <div class="betting-tables">
                <!-- 基础投注表格 -->
                <div class="bet-table">
                    <table>
                        <tr>
                            <td>
                                <button class="bet-option">
                                    <span class="bet-label">小(1-12)</span>
                                    <span class="bet-odds">x2</span>
                                    <span class="bet-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option">
                                    <span class="bet-label">0</span>
                                    <span class="bet-odds">x11</span>
                                    <span class="bet-amount">700</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option">
                                    <span class="bet-label">大(13-24)</span>
                                    <span class="bet-odds">x2</span>
                                    <span class="bet-amount">0</span>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <button class="bet-option">
                                    <span class="bet-label">单</span>
                                    <span class="bet-odds">x2</span>
                                    <span class="bet-amount">0</span>
                                </button>
                            </td>
                            <td></td>
                            <td>
                                <button class="bet-option">
                                    <span class="bet-label">双</span>
                                    <span class="bet-odds">x2</span>
                                    <span class="bet-amount">0</span>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <button class="bet-option">
                                    <span class="bet-label">1-8</span>
                                    <span class="bet-odds">x3</span>
                                    <span class="bet-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option">
                                    <span class="bet-label">9-16</span>
                                    <span class="bet-odds">x3</span>
                                    <span class="bet-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option">
                                    <span class="bet-label">17-24</span>
                                    <span class="bet-odds">x3</span>
                                    <span class="bet-amount">0</span>
                                </button>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 区间投注表格 -->
                <div class="bet-table">
                    <table>
                        <tr>
                            <td><button class="bet-option"><span class="bet-label">1-6</span><span class="bet-odds">x4</span><span class="bet-amount">10</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">7-12</span><span class="bet-odds">x4</span><span class="bet-amount">10</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">13-18</span><span class="bet-odds">x4</span><span class="bet-amount">10</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">19-24</span><span class="bet-odds">x4</span><span class="bet-amount">0</span></button></td>
                        </tr>
                        <tr>
                            <td><button class="bet-option"><span class="bet-label">1-3</span><span class="bet-odds">x8</span><span class="bet-amount">10</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">7-9</span><span class="bet-odds">x8</span><span class="bet-amount">40</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">13-15</span><span class="bet-odds">x8</span><span class="bet-amount">20</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">19-21</span><span class="bet-odds">x8</span><span class="bet-amount">0</span></button></td>
                        </tr>
                        <tr>
                            <td><button class="bet-option"><span class="bet-label">4-6</span><span class="bet-odds">x8</span><span class="bet-amount">30</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">10-12</span><span class="bet-odds">x8</span><span class="bet-amount">10</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">16-18</span><span class="bet-odds">x8</span><span class="bet-amount">10</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">22-24</span><span class="bet-odds">x8</span><span class="bet-amount">30</span></button></td>
                        </tr>
                    </table>
                </div>

                <!-- 单号投注表格 -->
                <div class="bet-table">
                    <table>
                        <tr>
                            <td><button class="bet-option"><span class="bet-label">1</span><span class="bet-odds">x24</span><span class="bet-amount">230</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">7</span><span class="bet-odds">x24</span><span class="bet-amount">80</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">13</span><span class="bet-odds">x24</span><span class="bet-amount">110</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">19</span><span class="bet-odds">x24</span><span class="bet-amount">60</span></button></td>
                        </tr>
                        <tr>
                            <td><button class="bet-option"><span class="bet-label">2</span><span class="bet-odds">x24</span><span class="bet-amount">60</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">8</span><span class="bet-odds">x24</span><span class="bet-amount">230</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">14</span><span class="bet-odds">x24</span><span class="bet-amount">200</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">20</span><span class="bet-odds">x24</span><span class="bet-amount">100</span></button></td>
                        </tr>
                        <tr>
                            <td><button class="bet-option"><span class="bet-label">3</span><span class="bet-odds">x24</span><span class="bet-amount">450</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">9</span><span class="bet-odds">x24</span><span class="bet-amount">420</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">15</span><span class="bet-odds">x24</span><span class="bet-amount">650</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">21</span><span class="bet-odds">x24</span><span class="bet-amount">360</span></button></td>
                        </tr>
                        <tr>
                            <td><button class="bet-option"><span class="bet-label">4</span><span class="bet-odds">x24</span><span class="bet-amount">250</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">10</span><span class="bet-odds">x24</span><span class="bet-amount">320</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">16</span><span class="bet-odds">x24</span><span class="bet-amount">200</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">22</span><span class="bet-odds">x24</span><span class="bet-amount">460</span></button></td>
                        </tr>
                        <tr>
                            <td><button class="bet-option"><span class="bet-label">5</span><span class="bet-odds">x24</span><span class="bet-amount">400</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">11</span><span class="bet-odds">x24</span><span class="bet-amount">340</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">17</span><span class="bet-odds">x24</span><span class="bet-amount">330</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">23</span><span class="bet-odds">x24</span><span class="bet-amount">220</span></button></td>
                        </tr>
                        <tr>
                            <td><button class="bet-option"><span class="bet-label">6</span><span class="bet-odds">x24</span><span class="bet-amount">280</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">12</span><span class="bet-odds">x24</span><span class="bet-amount">220</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">18</span><span class="bet-odds">x24</span><span class="bet-amount">100</span></button></td>
                            <td><button class="bet-option"><span class="bet-label">24</span><span class="bet-odds">x24</span><span class="bet-amount">280</span></button></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- 刷新按钮 -->
            <div class="refresh-section">
                <button class="refresh-btn" onclick="refreshGame()">刷新</button>
            </div>
        </div>
    </div>

    <script>
        function logout() {
            alert('退出功能');
        }

        function refreshGame() {
            alert('刷新功能');
        }

        // 倒计时功能
        function updateCountdown() {
            const countdownElement = document.getElementById('countdown');
            let timeLeft = parseInt(countdownElement.textContent);
            
            if (timeLeft > 0) {
                timeLeft--;
                countdownElement.textContent = timeLeft;
            }
        }

        // 每秒更新倒计时
        setInterval(updateCountdown, 1000);
    </script>
</body>
</html>
