<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>彩票直播游戏 - 新布局演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding: 0;
            border: 0;
            margin: 0;
            cursor: default;
            color: #888;
            background-color: #333;
            text-align: center;
            font-family: Helvetica, Verdana, Arial, sans-serif;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* 主容器 - 占据屏幕90%，居中显示 */
        .game-container {
            width: 90vw;
            height: 90vh;
            margin: 0 auto;
            display: flex;
            background-color: #333;
            position: relative;
            top: 50%;
            transform: translateY(-50%);
            min-width: 1000px;
            min-height: 600px;
            border: 2px solid #555;
        }

        /* 左侧区域 */
        .left-section {
            width: 40%;
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #073430;
        }

        /* 视频区域 */
        .video-section {
            width: 100%;
            height: 37.5%;
            background: #000;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 2vw;
            border-bottom: 1px solid #555;
        }

        /* 游戏信息区域 */
        .game-info-section {
            width: 100%;
            height: 12.5%;
            background-color: #073430;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 20px;
            color: #fff;
            font-size: 2vw;
            border-bottom: 1px solid #555;
        }

        .game-version, .game-round {
            text-align: center;
        }

        .countdown-section {
            text-align: center;
        }

        .countdown-label {
            font-size: 1.5vw;
            color: #fff;
        }

        .countdown-time {
            font-size: 2.5vw;
            color: #ff6b6b;
            font-weight: bold;
        }

        /* 历史号码区域 */
        .history-section {
            width: 100%;
            height: 25%;
            background-color: #073430;
            padding: 10px;
            overflow: hidden;
            border-bottom: 1px solid #555;
        }

        .history-numbers {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            justify-content: flex-start;
            align-content: flex-start;
            height: 100%;
            overflow: hidden;
        }

        .history-number {
            width: 3.5vw;
            height: 4vw;
            line-height: 4vw;
            font-size: 2vw;
            color: #fff;
            background-color: #a6a6a6;
            margin: 0;
            text-align: center;
            min-width: 40px;
            min-height: 45px;
        }

        /* 用户信息区域 */
        .user-section {
            width: 100%;
            height: 25%;
            background-color: #073430;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .user-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #fff;
            font-size: 2vw;
        }

        .username {
            color: #fff;
            font-weight: bold;
        }

        .balance {
            color: orange;
        }

        .logout-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: #fff;
            font-size: 2vw;
            line-height: 3.5vw;
            width: 8vw;
            height: 3.5vw;
            border: none;
            cursor: pointer;
            margin: 20px auto;
            border-radius: 8px;
            min-width: 80px;
            min-height: 40px;
        }

        .logout-btn:hover {
            opacity: 0.8;
        }

        /* 右侧投注区域 */
        .right-section {
            width: 60%;
            height: 100%;
            background-color: #333;
            display: flex;
            flex-direction: column;
        }

        /* 投注表格 */
        .betting-tables {
            flex: 1;
            padding: 10px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .bet-table {
            width: 100%;
        }

        .bet-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .bet-table td {
            padding: 2px;
            margin: 0;
        }

        /* 投注按钮样式 */
        .bet-option {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: #fff;
            font-size: 1.5vw;
            height: 5vw;
            line-height: 5vw;
            position: relative;
            cursor: pointer;
            border: none;
            width: 100%;
            text-align: center;
            border-radius: 4px;
            min-height: 50px;
        }

        .bet-option:hover {
            opacity: 0.8;
        }

        .bet-option .bet-label {
            font-size: 1.5vw;
            margin-left: 5px;
        }

        .bet-option .bet-odds {
            position: absolute;
            top: 0.3vw;
            right: 0.3vw;
            height: 1.5vw;
            line-height: 1.5vw;
            font-size: 1.3vw;
        }

        .bet-option .bet-amount {
            position: absolute;
            left: 0.3vw;
            top: 1.5vw;
            height: 2vw;
            line-height: 2vw;
            display: inline-block;
            margin: 0px;
            font-size: 1.2vw;
        }

        /* 刷新按钮 */
        .refresh-section {
            height: 8%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
        }

        .refresh-btn {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: #fff;
            font-size: 2vw;
            line-height: 3.5vw;
            width: 10vw;
            height: 3.5vw;
            border: none;
            cursor: pointer;
            border-radius: 8px;
            min-width: 100px;
            min-height: 40px;
        }

        .refresh-btn:hover {
            opacity: 0.8;
        }

        /* 标题 */
        .demo-title {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            color: #fff;
            font-size: 24px;
            background: rgba(0,0,0,0.7);
            padding: 10px 20px;
            border-radius: 8px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="demo-title">新布局演示 - 占据屏幕90%</div>
    
    <div class="game-container">
        <!-- 左侧区域 -->
        <div class="left-section">
            <!-- 视频区域 -->
            <div class="video-section">
                视频播放区域 (37.5%高度)
            </div>

            <!-- 游戏信息区域 -->
            <div class="game-info-section">
                <div class="game-version">
                    <div>第</div>
                    <div id="versionNumber">5</div>
                    <div>版</div>
                </div>
                <div class="game-round">
                    <div>第</div>
                    <div id="roundNumber">40</div>
                    <div>局</div>
                </div>
                <div class="countdown-section">
                    <div class="countdown-label">倒计时:</div>
                    <div class="countdown-time" id="countdown">22</div>
                </div>
            </div>

            <!-- 历史号码区域 -->
            <div class="history-section">
                <div class="history-numbers" id="historyNumbers">
                    <div class="history-number">24</div>
                    <div class="history-number">1</div>
                    <div class="history-number">11</div>
                    <div class="history-number">12</div>
                    <div class="history-number">19</div>
                    <div class="history-number">6</div>
                    <div class="history-number">21</div>
                    <div class="history-number">1</div>
                    <div class="history-number">18</div>
                    <div class="history-number">11</div>
                    <div class="history-number">10</div>
                    <div class="history-number">9</div>
                    <div class="history-number">10</div>
                    <div class="history-number">24</div>
                    <div class="history-number">7</div>
                    <div class="history-number">0</div>
                </div>
            </div>

            <!-- 用户信息区域 -->
            <div class="user-section">
                <div class="user-info">
                    <div class="username">6117</div>
                    <div class="balance">
                        积分: <span id="userBalance">1000.00</span>
                    </div>
                </div>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>

        <!-- 右侧投注区域 -->
        <div class="right-section">
            <div class="betting-tables">
                <!-- 基础投注表格 -->
                <div class="bet-table">
                    <table>
                        <tr>
                            <td>
                                <button class="bet-option">
                                    <span class="bet-label">小(1-12)</span>
                                    <span class="bet-odds">x2</span>
                                    <span class="bet-amount">0</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option">
                                    <span class="bet-label">0</span>
                                    <span class="bet-odds">x11</span>
                                    <span class="bet-amount">700</span>
                                </button>
                            </td>
                            <td>
                                <button class="bet-option">
                                    <span class="bet-label">大(13-24)</span>
                                    <span class="bet-odds">x2</span>
                                    <span class="bet-amount">0</span>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <button class="bet-option">
                                    <span class="bet-label">单</span>
                                    <span class="bet-odds">x2</span>
                                    <span class="bet-amount">0</span>
                                </button>
                            </td>
                            <td></td>
                            <td>
                                <button class="bet-option">
                                    <span class="bet-label">双</span>
                                    <span class="bet-odds">x2</span>
                                    <span class="bet-amount">0</span>
                                </button>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- 刷新按钮 -->
            <div class="refresh-section">
                <button class="refresh-btn" onclick="refreshGame()">刷新</button>
            </div>
        </div>
    </div>

    <script>
        function logout() {
            alert('退出功能');
        }

        function refreshGame() {
            alert('刷新功能');
        }

        // 倒计时功能
        function updateCountdown() {
            const countdownElement = document.getElementById('countdown');
            let timeLeft = parseInt(countdownElement.textContent);
            
            if (timeLeft > 0) {
                timeLeft--;
                countdownElement.textContent = timeLeft;
            }
        }

        // 每秒更新倒计时
        setInterval(updateCountdown, 1000);
    </script>
</body>
</html>
